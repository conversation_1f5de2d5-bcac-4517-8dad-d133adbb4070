# 结算接口JMeter压测使用说明

## 📋 脚本概述

本JMeter脚本专门用于对 `/v1/calculator/calculate` 结算接口进行性能压测，模拟真实用户的商品结算请求。

## 🔧 配置参数

### 全局变量配置
在测试计划中需要配置以下变量：

| 变量名 | 默认值 | 说明 | 示例 |
|--------|--------|------|------|
| `BASE_URL` | `https://your-domain.com` | 测试环境域名 | `https://test-api.example.com` |
| `ACCESS_TOKEN` | `your_access_token_here` | 用户认证Token | `Bearer eyJhbGciOiJIUzI1NiIs...` |
| `PRODUCT_TYPE` | `1` | 商品类型 | `1`(卡券) `2`(套餐) `3`(应用) |
| `PRODUCT_SPU_ID` | `100001` | 商品SPU ID | `100001` |
| `PRODUCT_SKU_ID` | `200001` | 商品SKU ID | `200001` |

### 线程组配置
- **线程数**: 100个并发用户
- **Ramp-up时间**: 60秒（逐步启动）
- **循环次数**: 10次
- **测试持续时间**: 300秒（5分钟）

## 📊 压测场景设计

### 请求参数说明
```json
{
  "productType": 1,           // 商品类型：1-卡券 2-套餐 3-应用
  "productSpuId": 100001,     // 商品SPU ID
  "productSkuId": 200001,     // 商品SKU ID（可选，无SKU时传0）
  "quantity": 1-5,            // 随机购买数量1-5件
  "rechargeAccount": "",      // 充值账号（视频直充时使用）
  "rechargeAccountType": 0,   // 充值账号类型
  "appProductPrice": null,    // 应用商品价格（应用类型时必传）
  "giftTickets": [],          // 赠送券列表
  "longitude": 116.**********, // 经度（北京坐标）
  "latitude": 39.**********,   // 纬度（北京坐标）
  "ydBusinessId": "test_business_xxxx", // 易盾业务ID
  "ydToken": "test_token_xxxxx",        // 易盾Token
  "captchaType": "",          // 验证码类型
  "captchaToken": "",         // 验证码Token
  "captchaCoordinates": "",   // 验证码坐标
  "timestamp": **********,    // 时间戳
  "nonce": "123456",          // 随机数
  "sign": "test_sign_xxxxxxx" // 签名
}
```

### 动态参数
- `quantity`: 使用 `${__Random(1,5)}` 随机生成1-5的购买数量
- `ydBusinessId`: 使用 `test_business_${__Random(1000,9999)}` 生成随机业务ID
- `ydToken`: 使用 `test_token_${__Random(10000,99999)}` 生成随机Token
- `timestamp`: 使用 `${__time()}` 生成当前时间戳
- `nonce`: 使用 `${__Random(100000,999999)}` 生成随机数
- `sign`: 使用 `test_sign_${__Random(1000000,9999999)}` 生成随机签名

## ✅ 断言验证

### 1. HTTP响应码断言
- 验证HTTP状态码为200
- 失败时显示"结算接口响应异常"

### 2. JSON路径断言
- 验证响应JSON中 `$.code` 字段值为200
- 确保业务逻辑正常执行

### 3. 响应时间断言
- 验证响应时间小于1500ms
- 符合接口性能要求（< 1500ms）

## 📈 监控指标

### 结果收集器
1. **查看结果树**: 详细查看每个请求的响应内容
2. **聚合报告**: 统计平均响应时间、吞吐量、错误率等
3. **TPS监控**: 实时监控每秒事务数

### 关键指标
- **平均响应时间**: < 1000ms
- **95%响应时间**: < 1500ms
- **错误率**: < 1%
- **TPS**: 目标 500-1000
- **成功率**: > 99%

## 🚀 执行步骤

### 1. 环境准备
```bash
# 确保JMeter已安装
java -version
jmeter -version

# 创建结果目录
mkdir jmeter-results
```

### 2. 参数配置
1. 打开JMeter脚本文件
2. 修改测试计划中的全局变量
3. 根据实际环境调整BASE_URL和ACCESS_TOKEN
4. 配置真实的商品ID

### 3. 执行压测
```bash
# GUI模式（调试用）
jmeter -t 结算接口JMeter压测脚本.jmx

# 命令行模式（正式压测）
jmeter -n -t 结算接口JMeter压测脚本.jmx -l results/calculate_test.jtl -e -o results/html-report
```

### 4. 结果分析
- 查看HTML报告：`results/html-report/index.html`
- 分析JTL文件：使用JMeter GUI打开结果文件
- 监控系统资源：CPU、内存、网络等

## ⚠️ 注意事项

### 压测环境要求
1. **使用测试环境**: 绝不在生产环境执行压测
2. **数据隔离**: 使用测试商品ID和用户数据
3. **服务保护**: 确保上游服务已配置Mock或限流

### 安全考虑
1. **Token管理**: 使用测试环境的Token，避免泄露生产Token
2. **签名验证**: 如果启用签名验证，需要实现正确的签名算法
3. **频率控制**: 避免过高的并发导致服务不可用

### 监控重点
1. **数据库连接**: 监控数据库连接池使用情况
2. **缓存命中**: 观察Redis缓存的命中率
3. **上游服务**: 监控风控、库存等上游服务状态
4. **系统资源**: CPU、内存、磁盘IO等系统指标

## 🔍 故障排查

### 常见问题
1. **认证失败**: 检查ACCESS_TOKEN是否有效
2. **参数错误**: 验证商品ID是否存在
3. **超时错误**: 调整连接超时和响应超时时间
4. **限流触发**: 降低并发数或增加等待时间

### 日志分析
- 查看应用日志中的错误信息
- 分析JMeter的jmeter.log文件
- 检查系统监控指标异常

## 📝 结果报告

压测完成后，需要整理以下内容：
1. 压测环境和配置信息
2. 关键性能指标统计
3. 错误分析和原因
4. 性能瓶颈识别
5. 优化建议和后续计划
