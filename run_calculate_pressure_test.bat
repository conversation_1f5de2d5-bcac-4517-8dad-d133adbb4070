@echo off
REM 结算接口压测执行脚本
REM 使用方法: run_calculate_pressure_test.bat [环境] [并发数] [持续时间]
REM 示例: run_calculate_pressure_test.bat test 50 300

setlocal enabledelayedexpansion

REM 默认参数
set ENV=test
set THREADS=100
set DURATION=300
set BASE_URL=https://test-api.example.com
set ACCESS_TOKEN=your_test_token_here

REM 解析命令行参数
if not "%1"=="" set ENV=%1
if not "%2"=="" set THREADS=%2
if not "%3"=="" set DURATION=%3

REM 根据环境设置不同的URL
if "%ENV%"=="test" (
    set BASE_URL=https://test-api.example.com
    set ACCESS_TOKEN=test_token_here
)
if "%ENV%"=="staging" (
    set BASE_URL=https://staging-api.example.com
    set ACCESS_TOKEN=staging_token_here
)

echo ========================================
echo 结算接口压测开始
echo ========================================
echo 环境: %ENV%
echo 基础URL: %BASE_URL%
echo 并发数: %THREADS%
echo 持续时间: %DURATION%秒
echo ========================================

REM 创建结果目录
set TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set RESULT_DIR=results\calculate_%ENV%_%TIMESTAMP%
mkdir "%RESULT_DIR%" 2>nul

REM 设置JMeter参数
set JMETER_OPTS=-Xms1g -Xmx4g -XX:MaxMetaspaceSize=256m

REM 执行压测
echo 开始执行压测...
jmeter -n ^
    -t "结算接口JMeter压测脚本.jmx" ^
    -l "%RESULT_DIR%\calculate_results.jtl" ^
    -e -o "%RESULT_DIR%\html-report" ^
    -JBASE_URL=%BASE_URL% ^
    -JACCESS_TOKEN=%ACCESS_TOKEN% ^
    -JThreads=%THREADS% ^
    -JDuration=%DURATION% ^
    -JPRODUCT_TYPE=1 ^
    -JPRODUCT_SPU_ID=100001 ^
    -JPRODUCT_SKU_ID=200001

if %ERRORLEVEL% EQU 0 (
    echo ========================================
    echo 压测执行完成！
    echo ========================================
    echo 结果文件: %RESULT_DIR%\calculate_results.jtl
    echo HTML报告: %RESULT_DIR%\html-report\index.html
    echo ========================================
    
    REM 打开HTML报告
    start "" "%RESULT_DIR%\html-report\index.html"
    
    REM 显示简要统计
    echo 正在生成简要统计...
    powershell -Command "& {
        $jtl = Import-Csv '%RESULT_DIR%\calculate_results.jtl' -Delimiter '`t'
        $total = $jtl.Count
        $success = ($jtl | Where-Object {$_.success -eq 'true'}).Count
        $avgTime = [math]::Round(($jtl.elapsed | Measure-Object -Average).Average, 2)
        $maxTime = ($jtl.elapsed | Measure-Object -Maximum).Maximum
        $minTime = ($jtl.elapsed | Measure-Object -Minimum).Minimum
        $successRate = [math]::Round(($success / $total) * 100, 2)
        
        Write-Host '简要统计结果:'
        Write-Host '总请求数: ' $total
        Write-Host '成功请求数: ' $success
        Write-Host '成功率: ' $successRate '%'
        Write-Host '平均响应时间: ' $avgTime 'ms'
        Write-Host '最大响应时间: ' $maxTime 'ms'
        Write-Host '最小响应时间: ' $minTime 'ms'
    }"
    
) else (
    echo ========================================
    echo 压测执行失败！错误代码: %ERRORLEVEL%
    echo ========================================
    echo 请检查:
    echo 1. JMeter是否正确安装
    echo 2. 脚本文件是否存在
    echo 3. 网络连接是否正常
    echo 4. 目标服务是否可访问
    echo ========================================
)

pause
endlocal
