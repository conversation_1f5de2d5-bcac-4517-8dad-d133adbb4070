#!/bin/bash

# 结算接口压测执行脚本
# 使用方法: ./run_calculate_pressure_test.sh [环境] [并发数] [持续时间]
# 示例: ./run_calculate_pressure_test.sh test 50 300

# 默认参数
ENV=${1:-test}
THREADS=${2:-100}
DURATION=${3:-300}
BASE_URL="https://test-api.example.com"
ACCESS_TOKEN="your_test_token_here"

# 根据环境设置不同的URL
case $ENV in
    "test")
        BASE_URL="https://test-api.example.com"
        ACCESS_TOKEN="test_token_here"
        ;;
    "staging")
        BASE_URL="https://staging-api.example.com"
        ACCESS_TOKEN="staging_token_here"
        ;;
    "prod")
        echo "❌ 禁止在生产环境执行压测！"
        exit 1
        ;;
esac

echo "========================================"
echo "🚀 结算接口压测开始"
echo "========================================"
echo "环境: $ENV"
echo "基础URL: $BASE_URL"
echo "并发数: $THREADS"
echo "持续时间: ${DURATION}秒"
echo "========================================"

# 检查JMeter是否安装
if ! command -v jmeter &> /dev/null; then
    echo "❌ JMeter未安装或未添加到PATH"
    echo "请先安装JMeter: https://jmeter.apache.org/download_jmeter.cgi"
    exit 1
fi

# 检查脚本文件是否存在
SCRIPT_FILE="结算接口JMeter压测脚本.jmx"
if [ ! -f "$SCRIPT_FILE" ]; then
    echo "❌ 脚本文件不存在: $SCRIPT_FILE"
    exit 1
fi

# 创建结果目录
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RESULT_DIR="results/calculate_${ENV}_${TIMESTAMP}"
mkdir -p "$RESULT_DIR"

# 设置JMeter参数
export JMETER_OPTS="-Xms1g -Xmx4g -XX:MaxMetaspaceSize=256m"

echo "📊 开始执行压测..."

# 执行压测
jmeter -n \
    -t "$SCRIPT_FILE" \
    -l "$RESULT_DIR/calculate_results.jtl" \
    -e -o "$RESULT_DIR/html-report" \
    -JBASE_URL="$BASE_URL" \
    -JACCESS_TOKEN="$ACCESS_TOKEN" \
    -JThreads="$THREADS" \
    -JDuration="$DURATION" \
    -JPRODUCT_TYPE=1 \
    -JPRODUCT_SPU_ID=100001 \
    -JPRODUCT_SKU_ID=200001

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "========================================"
    echo "✅ 压测执行完成！"
    echo "========================================"
    echo "结果文件: $RESULT_DIR/calculate_results.jtl"
    echo "HTML报告: $RESULT_DIR/html-report/index.html"
    echo "========================================"
    
    # 生成简要统计
    if [ -f "$RESULT_DIR/calculate_results.jtl" ]; then
        echo "📈 简要统计结果:"
        
        # 使用awk分析JTL文件
        awk -F'\t' '
        NR > 1 {
            total++
            if ($8 == "true") success++
            sum += $2
            if (NR == 2 || $2 > max) max = $2
            if (NR == 2 || $2 < min) min = $2
        }
        END {
            if (total > 0) {
                printf "总请求数: %d\n", total
                printf "成功请求数: %d\n", success
                printf "成功率: %.2f%%\n", (success/total)*100
                printf "平均响应时间: %.2f ms\n", sum/total
                printf "最大响应时间: %d ms\n", max
                printf "最小响应时间: %d ms\n", min
            }
        }' "$RESULT_DIR/calculate_results.jtl"
    fi
    
    echo "========================================"
    echo "📋 后续操作:"
    echo "1. 查看详细报告: open $RESULT_DIR/html-report/index.html"
    echo "2. 分析JTL文件: 使用JMeter GUI打开结果文件"
    echo "3. 监控系统资源: 检查CPU、内存、网络等指标"
    echo "========================================"
    
else
    echo "========================================"
    echo "❌ 压测执行失败！错误代码: $?"
    echo "========================================"
    echo "请检查:"
    echo "1. JMeter是否正确安装"
    echo "2. 脚本文件是否存在"
    echo "3. 网络连接是否正常"
    echo "4. 目标服务是否可访问"
    echo "5. 查看jmeter.log获取详细错误信息"
    echo "========================================"
    exit 1
fi

# 询问是否打开HTML报告
read -p "是否打开HTML报告? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v xdg-open &> /dev/null; then
        xdg-open "$RESULT_DIR/html-report/index.html"
    elif command -v open &> /dev/null; then
        open "$RESULT_DIR/html-report/index.html"
    else
        echo "请手动打开: $RESULT_DIR/html-report/index.html"
    fi
fi

echo "🎉 压测任务完成！"
