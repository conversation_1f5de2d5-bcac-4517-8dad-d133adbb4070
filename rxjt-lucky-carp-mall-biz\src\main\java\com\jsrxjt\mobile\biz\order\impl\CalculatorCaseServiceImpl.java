package com.jsrxjt.mobile.biz.order.impl;

import com.jsrxjt.common.core.constant.RedisKeyConstants;
import com.jsrxjt.common.core.exception.BizException;
import com.jsrxjt.common.core.util.cache.RedisUtil;
import com.jsrxjt.mobile.api.order.dp.ProductItemId;
import com.jsrxjt.mobile.api.order.dto.request.SettleProductDTO;
import com.jsrxjt.mobile.api.order.dto.response.GiftTicketResponseDTO;
import com.jsrxjt.mobile.api.order.dto.response.SettleResponseDTO;
import com.jsrxjt.mobile.api.product.types.ProductTypeEnum;
import com.jsrxjt.mobile.api.riskcontrol.dto.response.ProductRiskControlResponse;
import com.jsrxjt.mobile.api.riskcontrol.types.RiskBusinessTypeEnum;
import com.jsrxjt.mobile.biz.order.CalculatorCaseService;
import com.jsrxjt.mobile.biz.order.GiftTicketValidationService;
import com.jsrxjt.mobile.domain.inventory.service.SkuReleaseInventoryService;
import com.jsrxjt.mobile.domain.order.entity.OrderAmountResult;
import com.jsrxjt.mobile.domain.order.entity.PurchaseInfo;
import com.jsrxjt.mobile.domain.order.service.CalculateAmountService;
import com.jsrxjt.mobile.domain.packages.entity.PackageSubSkuEntity;
import com.jsrxjt.mobile.domain.product.entity.ProductItem;
import com.jsrxjt.mobile.domain.product.service.ProductItemDomainService;
import com.jsrxjt.mobile.domain.product.service.ProductSkuSellRegionService;
import com.jsrxjt.mobile.domain.product.service.validator.ProductInventoryValidationService;
import com.jsrxjt.mobile.domain.promotion.service.validator.PromotionActivityValidationService;
import com.jsrxjt.mobile.domain.region.entity.RegionEntity;
import com.jsrxjt.mobile.domain.region.repository.RegionRepository;
import com.jsrxjt.mobile.domain.riskcontrol.service.ProductRiskControlService;
import com.jsrxjt.mobile.domain.rxmember.request.RxMemberRiskRequest;
import com.jsrxjt.mobile.domain.rxmember.service.RxMemberRiskService;
import com.jsrxjt.mobile.domain.ticket.entity.GiftTicketInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 产品结算服务实现
 * 
 * <AUTHOR> Fengping
 * @since 2025/6/25
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class CalculatorCaseServiceImpl implements CalculatorCaseService {

    private final ProductItemDomainService productItemDomainService;
    private final CalculateAmountService calculateAmountService;

    private final ProductRiskControlService productRiskControlService;

    private final ProductInventoryValidationService productInventoryValidationService;

    private final PromotionActivityValidationService promotionActivityValidationService;

    private final SkuReleaseInventoryService skuReleaseInventoryService;

    private final ProductSkuSellRegionService productSkuSellRegionService;

    private final RegionRepository regionRepository;

    private final GiftTicketValidationService giftTicketValidationService;

    private final RxMemberRiskService rxMemberRiskService;

    private final RedisUtil redisUtil;

    @Override
    public SettleResponseDTO calculateAmount(SettleProductDTO dto) {
        log.info("开始产品结算，客户ID：{}, 产品类型：{}, SPU ID：{}, SKU ID：{}, 数量：{}",
                dto.getCustomerId(), dto.getProductType(), dto.getProductSpuId(), dto.getProductSkuId(),
                dto.getQuantity());

        // 1. 参数校验
        validateSettleRequest(dto);

        // 2. 构建统一商品信息
        ProductItemId productItemId = ProductItemId.of(
                dto.getProductSpuId(),
                dto.getProductSkuId(),
                dto.getProductType());
        ProductItem productItem = buildProductItem(productItemId);

        // 3. 可售性校验
        validateProductAvailability(productItem);
        Integer regionId;
        if (isLoadTestProfile()) {
            regionId = 37;
        } else {
            RegionEntity currentRegion = regionRepository.getCurrentRegion(dto.getCustomerId());
            regionId = currentRegion.getId();
        }

        // 4. 库存和放量校验
        // 活动限购校验
        promotionActivityValidationService.validateActivityQuota(productItem, dto.getCustomerId(), dto.getQuantity());

        if (!isLoadTestProfile()) {
            productInventoryValidationService.validateInventory(productItem, dto.getQuantity());
        }

        // 放量校验
        if (productItem.hasReleaseInventoryProp()) {
            skuReleaseInventoryService.checkAndGetSkuReleaseInventories(productItemId, regionId, dto.getQuantity());
        }

        // 5. 当月，当日 购买数量获取
        Integer monthlyPurchaseQuantity = calculateAmountService.getMonthlyPurchaseQuantity(dto.getCustomerId(),
                productItemId);
        Integer dailyPurchaseQuantity = calculateAmountService.getDailyPurchaseQuantity(dto.getCustomerId(),
                productItemId);

        // 6. 补充应用的商品价格（如果需要）
        supplementAppProductPrice(productItem, dto);

        // 7. 校验和处理赠送券信息
        List<GiftTicketInfo> giftTickets = giftTicketValidationService.validateAndProcessGiftTickets(
                productItemId, dto.getGiftTickets(), dto.getQuantity());
        List<GiftTicketResponseDTO> giftTicketsDTO = giftTickets.stream()
                .map(giftTicketInfo -> {
                    GiftTicketResponseDTO ticketResponseDTO = new GiftTicketResponseDTO();
                    ticketResponseDTO.setTicketId(giftTicketInfo.getTicketId());
                    ticketResponseDTO.setTicketName(giftTicketInfo.getTicketName());
                    ticketResponseDTO.setSpecPicUrl(giftTicketInfo.getSpecPicUrl());
                    ticketResponseDTO.setTicketNum(giftTicketInfo.getTicketNum());
                    return ticketResponseDTO;
                })
                .toList();

        // 8. 获取订单金额信息
        OrderAmountResult orderAmountResult = calculateOrderAmount(productItem, dto, regionId);
        // 如果命中风控，风控的当月，当日购买数量优先有效
        if (orderAmountResult.getRiskControl().isHitStrategy()) {
            productItem.setLimitNumPerDay(orderAmountResult.getRiskControl().getLimitNumPerDay());
            productItem.setLimitNumPerMonth(orderAmountResult.getRiskControl().getLimitNumPerMonth());

        }
        // 9. 验证日、月购买数量限制
        validateDailyLimit(productItem, dailyPurchaseQuantity, dto.getQuantity());
        validateMonthlyLimit(productItem, monthlyPurchaseQuantity, dto.getQuantity());

        // 10. 构建结算响应
        SettleResponseDTO response = buildSettleResponse(orderAmountResult, productItem, monthlyPurchaseQuantity,
                dailyPurchaseQuantity,
                dto.getQuantity());
        response.setGiftTickets(giftTicketsDTO);

        log.info("产品结算完成，客户ID：{}, 订单金额：{}, 支付金额：{}",
                dto.getCustomerId(), response.getOrderAmount(), response.getPayAmount());

        return response;
    }

    private boolean isLoadTestProfile() {
        return true;
    }

    /**
     * 参数校验
     */
    private void validateSettleRequest(SettleProductDTO dto) {
        if (dto.getProductType() == null) {
            throw new BizException("产品类型不能为空");
        }
        if (dto.getProductSpuId() == null || dto.getProductSpuId() <= 0) {
            throw new BizException("产品SPU ID不能为空");
        }
        if (dto.getQuantity() == null || dto.getQuantity() <= 0) {
            throw new BizException("购买数量必须大于0");
        }
    }

    /**
     * 构建统一商品信息
     */
    private ProductItem buildProductItem(ProductItemId productItemId) {

        try {
            return productItemDomainService.buildProductItem(productItemId);
        } catch (Exception e) {
            log.error("构建商品信息失败，productItemId：{}", productItemId, e);
            throw new BizException("商品信息不存在或已下架");
        }
    }

    /**
     * 可售性校验
     */
    private void validateProductAvailability(ProductItem productItem) {
        if (!productItem.isAvailable()) {
            throw new BizException("商品不可售");
        }
    }

    /**
     * 单月购买数量限制校验
     */
    private void validateMonthlyLimit(ProductItem productItem, Integer monthlyPurchaseQuantity, Integer quantity) {
        if (productItem.hasLimitNumPerMonth()
                && (monthlyPurchaseQuantity + quantity > productItem.getLimitNumPerMonth())) {
            throw new BizException("单月购买数量超出限制,限购" + productItem.getLimitNumPerMonth() + "张，本月已购买"
                    + monthlyPurchaseQuantity + "张");

        }

    }

    private void validateDailyLimit(ProductItem productItem, Integer dailyPurchaseQuantity, Integer quantity) {
        if (productItem.hasLimitNumPerDay()
                && (dailyPurchaseQuantity + quantity > productItem.getLimitNumPerDay())) {
            throw new BizException("单日购买数量超出限制,限购" + productItem.getLimitNumPerDay() + "张，今日已购买"
                    + dailyPurchaseQuantity + "张");
        }
    }

    /**
     * 补充应用的商品价格
     */
    private void supplementAppProductPrice(ProductItem productItem, SettleProductDTO dto) {
        if (Objects.equals(productItem.getProductType(), ProductTypeEnum.APP.getType())
                && productItem.getPlatformPrice() == null) {
            productItem.setPlatformPrice(dto.getAppProductPrice());
        }
    }

    /**
     * 计算订单金额
     */
    private OrderAmountResult calculateOrderAmount(ProductItem productItem, SettleProductDTO dto, Integer regionId) {

        PurchaseInfo purchaseInfo = PurchaseInfo.of(
                productItem,
                dto.getQuantity(),
                regionId,
                dto.getCustomerId());
        Integer customerRiskLevel;
        if (isLoadTestProfile()) {
            customerRiskLevel = 0;
        } else {
            customerRiskLevel = reportPlaceOrderAndGetCustomerRiskLevel(productItem, dto);
        }
        ProductRiskControlResponse riskControl = productRiskControlService.getProductRiskControl(ProductItemId
                .of(productItem.getSpuId(), productItem.getSkuId(), productItem.getProductType()),
                dto.getCustomerId(), customerRiskLevel);

        return calculateAmountService.calculateOrderAmount(purchaseInfo, riskControl);
    }

    /**
     * 构建结算响应
     */
    private SettleResponseDTO buildSettleResponse(OrderAmountResult orderAmountResult, ProductItem productItem,
            Integer monthlyPurchaseQuantity,Integer dailyPurchaseQuantity, Integer quantity) {
        SettleResponseDTO response = new SettleResponseDTO();

        // 原价销售价金额
        response.setTotalSellAmount(orderAmountResult.getTotalSellAmount());

        // 加点手续费
        response.setTotalServiceFee(orderAmountResult.getTotalServiceFee());
        response.setSingleGoodsServiceFee(orderAmountResult.getSingleGoodsServiceFee());
        response.setServiceFeeRate(orderAmountResult.getServiceFeeRate());

        // 超额相关
        response.setTotalExceedFee(orderAmountResult.getTotalExceedFee());
        response.setExceedAmount(orderAmountResult.getExceedAmount());
        response.setExceedFee(orderAmountResult.getExceedFee());
        response.setExceedPercentage(orderAmountResult.getExceedPercentage());
        response.setIsExceedAllowed(orderAmountResult.getIsExceedAllowed());

        // 订单金额
        response.setOrderAmount(orderAmountResult.getOrderAmount());
        response.setPayAmount(orderAmountResult.getPayAmount());

        // 限额和已购金额
        response.setLimitAmount(orderAmountResult.getLimitAmount());
        response.setPurchasedAmount(orderAmountResult.getPurchasedAmount());

        // 优惠相关
        response.setTotalDiscountAmount(orderAmountResult.getTotalDiscountAmount());
        response.setSingleGoodsDiscountAmount(orderAmountResult.getSingleGoodsDiscountAmount());

        // 折扣信息从商品的促销信息中获取
        if (productItem.getPromotionSkuInfo() != null && productItem.getPromotionSkuInfo().getDiscount() != null) {
            response.setDiscount(productItem.getPromotionSkuInfo().getDiscount());
        }

        // 购买数量相关
        response.setMonthlyPurchaseQuantity(monthlyPurchaseQuantity);
        response.setSkuLimitNumPerMonth(productItem.getLimitNumPerMonth());
        response.setDailyPurchaseQuantity(dailyPurchaseQuantity);
        response.setSkuLimitNumPerDay(productItem.getLimitNumPerDay());

        SettleResponseDTO.ProductInfo productInfo = buildProductInfo(productItem, quantity);
        response.setProductInfo(productInfo);
        return response;
    }

    private SettleResponseDTO.ProductInfo buildProductInfo(ProductItem productItem, Integer quantity) {
        SettleResponseDTO.ProductInfo productInfo = new SettleResponseDTO.ProductInfo();
        productInfo.setSpuId(productItem.getSpuId());
        productInfo.setSkuId(productItem.getSkuId());
        productInfo.setProductName(productItem.getProductName());
        productInfo.setProductLogo(productItem.getImgUrl());
        productInfo.setProductType(productItem.getProductType());
        productInfo.setFlatProductType(productItem.getFlatProductType());
        productInfo.setQuantity(quantity);
        productInfo.setFaceAmount(productItem.getFaceAmount());
        productInfo.setPlatformPrice(productItem.getPlatformPrice());

        productInfo.setNeedCaptcha(productItem.isNeedCaptcha());

        // 套餐产品：设置子产品信息
        if (Objects.equals(productItem.getProductType(), ProductTypeEnum.PACKAGE.getType())) {
            productInfo.setSubProductList(buildPackageSubProductList(productItem));
        }
        return productInfo;
    }

    /**
     * 构建套餐子产品信息列表
     */
    private List<SettleResponseDTO.PackageSubProductInfo> buildPackageSubProductList(ProductItem productItem) {
        List<PackageSubSkuEntity> subSkus = productItem.getPackageSubSkus();
        if (subSkus == null || subSkus.isEmpty()) {
            return Collections.emptyList();
        }

        // 转换为响应DTO
        return subSkus.stream()
                .map(subSku -> {
                    SettleResponseDTO.PackageSubProductInfo subProductInfo = new SettleResponseDTO.PackageSubProductInfo();
                    subProductInfo.setAmountName(subSku.getAmountName());
                    subProductInfo.setPackageCouponNum(subSku.getPackageCouponNum());
                    subProductInfo.setAmount(subSku.getAmount());
                    subProductInfo.setPackageCouponImg(subSku.getImgUrl());
                    return subProductInfo;
                })
                .toList();
    }

    /**
     * 上报下单数据并获取客户风控等级
     *
     * @param productItem 产品项
     * @param dto         结算产品DTO
     * @return 用户风控等级
     */
    private Integer reportPlaceOrderAndGetCustomerRiskLevel(ProductItem productItem, SettleProductDTO dto) {
        log.info("开始上报下单数据并获取客户风控等级，customerId：{}，productType：{}", dto.getCustomerId(), dto.getProductType());

        // 只有卡券产品（productType = 1）才需要上报风控数据
        if (!Objects.equals(dto.getProductType(), ProductTypeEnum.COUPON.getType())) {
            log.info("非卡券产品，不需要上报风控数据 直接返回0，productType：{}", dto.getProductType());
            return 0;
        }

        // 获取goodsId用于缓存key
        Long goodsId = 0L;
        if (productItem.getOutProductId() != null && !productItem.getOutProductId().isEmpty()) {
            try {
                goodsId = Long.parseLong(productItem.getOutProductId());
            } catch (NumberFormatException e) {
                log.warn("outProductId转换为Long失败，outProductId：{}", productItem.getOutProductId(), e);
            }
        }

        // 构建缓存key：risk:level:customer:{customerId}:{goodsId}:{quantity}
        String cacheKey = String.format(RedisKeyConstants.RISK_LEVEL_CUSTOMER,
                dto.getCustomerId(), goodsId, dto.getQuantity());


        //构建风控请求参数
        RxMemberRiskRequest riskRequest = new RxMemberRiskRequest();

        // 设置业务类型为下单
        riskRequest.setTypeEnum(RiskBusinessTypeEnum.PLACEORDER_TYPE);

        // 设置客户ID
        riskRequest.setCustomerId(dto.getCustomerId());

        // 设置IP地址，如果没有则使用"unknown"
        String ip = dto.getClientIp();
        if (ip == null || ip.trim().isEmpty()) {
            ip = "unknown";
        }
        riskRequest.setIP(ip);

        // 设置易盾token和businessId（前端传入）
        riskRequest.setToken(dto.getYdToken());
        riskRequest.setBusinessId(dto.getYdBusinessId());

        // 构建下单数据
        RxMemberRiskRequest.PlaceOrderData placeOrderData = new RxMemberRiskRequest.PlaceOrderData();

        // 设置卡券中心的goodsId
        if (goodsId > 0) {
            placeOrderData.setGoodsId(goodsId);
        }

        // 设置购买数量
        placeOrderData.setGoodsCount(dto.getQuantity());

        // 计算消费金额（面值 * 购买数量）
        BigDecimal consumption = BigDecimal.ZERO;
        if (productItem.getFaceAmount() != null && dto.getQuantity() != null) {
            consumption = productItem.getFaceAmount().multiply(new BigDecimal(dto.getQuantity()));
        }
        placeOrderData.setConsumption(consumption);

        // 设置下单数据到请求中
        riskRequest.setPlaceOrderData(placeOrderData);

        // 调用风控服务获取用户风控等级
        Integer riskLevel = rxMemberRiskService.getUserRiskLevel(riskRequest);

        log.info("获取客户风控等级成功，customerId：{}，riskLevel：{}", dto.getCustomerId(), riskLevel);

        // 将风控等级缓存1小时（3600秒）
        if (riskLevel != null) {
            redisUtil.set(cacheKey, String.valueOf(riskLevel), 3600);
            log.info("风控等级已缓存，customerId：{}，goodsId：{}，quantity：{}，riskLevel：{}，缓存时间：1小时",
                    dto.getCustomerId(), goodsId, dto.getQuantity(), riskLevel);
        }

        return riskLevel;
    }

}
